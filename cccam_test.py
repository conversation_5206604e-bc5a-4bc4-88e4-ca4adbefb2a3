#!/usr/bin/env python3
"""
CCcam Protocol Test Client

This script implements the CCcam protocol to test server connections.
CCcam is a card sharing protocol used in satellite TV reception.

Protocol Overview:
- Uses TCP connection with custom binary protocol
- Authentication via DES encryption
- Message format: [size][data]
- Handshake includes version exchange and authentication

Based on open-source implementations and protocol analysis.
"""

import socket
import struct
import hashlib
import time
from typing import Optional, Tuple, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CCcamProtocol:
    """CCcam protocol implementation for client connections."""
    
    # Protocol constants
    CCCAM_VERSION = 0x2928
    MSG_CLI_AUTH = 0x00
    MSG_SRV_LOGIN_ACK = 0x01
    MSG_SRV_LOGIN_NAK = 0x02
    MSG_CLI_DATA = 0x03
    MSG_SRV_DATA = 0x04
    
    def __init__(self, host: str, port: int, username: str, password: str, timeout: int = 10):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.timeout = timeout
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.authenticated = False
        
    def connect(self) -> bool:
        """Establish TCP connection to CCcam server."""
        try:
            logger.info(f"Connecting to {self.host}:{self.port}")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.host, self.port))
            self.connected = True
            logger.info("TCP connection established")
            return True
        except socket.timeout:
            logger.error("Connection timeout")
            return False
        except socket.gaierror as e:
            logger.error(f"DNS resolution failed: {e}")
            return False
        except ConnectionRefusedError:
            logger.error("Connection refused by server")
            return False
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    def _send_data(self, data: bytes) -> bool:
        """Send data with CCcam protocol format."""
        try:
            # CCcam format: 2-byte length + data
            length = len(data)
            packet = struct.pack('>H', length) + data
            self.socket.send(packet)
            logger.debug(f"Sent {len(packet)} bytes")
            return True
        except Exception as e:
            logger.error(f"Send failed: {e}")
            return False
    
    def _recv_data(self) -> Optional[bytes]:
        """Receive data using CCcam protocol format."""
        try:
            # Read 2-byte length header
            length_data = self._recv_exact(2)
            if not length_data:
                return None
            
            length = struct.unpack('>H', length_data)[0]
            if length == 0:
                return b''
            
            # Read the actual data
            data = self._recv_exact(length)
            logger.debug(f"Received {len(data) if data else 0} bytes")
            return data
        except Exception as e:
            logger.error(f"Receive failed: {e}")
            return None
    
    def _recv_exact(self, size: int) -> Optional[bytes]:
        """Receive exact number of bytes."""
        data = b''
        while len(data) < size:
            try:
                chunk = self.socket.recv(size - len(data))
                if not chunk:
                    logger.error("Connection closed by server")
                    return None
                data += chunk
            except socket.timeout:
                logger.error("Receive timeout")
                return None
        return data
    
    def _create_auth_hash(self, server_random: bytes) -> bytes:
        """Create authentication hash using CCcam algorithm."""
        # Simplified CCcam auth hash (actual implementation uses DES)
        # This is a basic implementation for demonstration
        auth_string = f"{self.username}{self.password}".encode()
        combined = server_random + auth_string
        return hashlib.sha1(combined).digest()[:16]
    
    def perform_handshake(self) -> bool:
        """Perform CCcam authentication handshake."""
        try:
            logger.info("Starting authentication handshake")
            
            # Step 1: Receive server hello
            hello_data = self._recv_data()
            if not hello_data or len(hello_data) < 16:
                logger.error("Invalid server hello")
                return False
            
            server_random = hello_data[:8]
            server_version = struct.unpack('>H', hello_data[14:16])[0]
            logger.info(f"Server version: 0x{server_version:04x}")
            
            # Step 2: Send client authentication
            client_random = b'\x01\x02\x03\x04\x05\x06\x07\x08'  # Should be random
            auth_hash = self._create_auth_hash(server_random)
            
            auth_packet = (
                client_random +
                struct.pack('>H', self.CCCAM_VERSION) +
                self.username.encode().ljust(20, b'\x00')[:20] +
                auth_hash
            )
            
            if not self._send_data(auth_packet):
                return False
            
            # Step 3: Receive authentication response
            auth_response = self._recv_data()
            if not auth_response:
                logger.error("No authentication response")
                return False
            
            if len(auth_response) > 0 and auth_response[0] == self.MSG_SRV_LOGIN_ACK:
                logger.info("Authentication successful")
                self.authenticated = True
                return True
            else:
                logger.error("Authentication failed")
                return False
                
        except Exception as e:
            logger.error(f"Handshake failed: {e}")
            return False
    
    def get_server_info(self) -> Dict[str, Any]:
        """Retrieve server information."""
        info = {
            'connected': self.connected,
            'authenticated': self.authenticated,
            'host': self.host,
            'port': self.port,
            'version': 'Unknown',
            'build': 'Unknown',
            'cards': 0,
            'providers': []
        }
        
        if not self.authenticated:
            return info
        
        try:
            # Send info request (simplified)
            info_request = struct.pack('B', 0x05)  # Info request command
            if self._send_data(info_request):
                response = self._recv_data()
                if response:
                    # Parse response (simplified)
                    info['cards'] = len(response) // 4 if len(response) > 0 else 0
                    logger.info(f"Server has {info['cards']} cards")
        except Exception as e:
            logger.error(f"Failed to get server info: {e}")
        
        return info
    
    def test_connection(self) -> Dict[str, Any]:
        """Test complete CCcam connection."""
        logger.info("=== CCcam Connection Test ===")
        logger.info(f"Target: {self.host}:{self.port}")
        logger.info(f"Username: {self.username}")
        
        result = {
            'success': False,
            'error': None,
            'connection_time': None,
            'server_info': {}
        }
        
        start_time = time.time()
        
        try:
            # Step 1: Connect
            if not self.connect():
                result['error'] = 'Connection failed'
                return result
            
            # Step 2: Authenticate
            if not self.perform_handshake():
                result['error'] = 'Authentication failed'
                return result
            
            # Step 3: Get server info
            result['server_info'] = self.get_server_info()
            result['connection_time'] = time.time() - start_time
            result['success'] = True
            
            logger.info("=== Connection Test Successful ===")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Test failed: {e}")
        
        finally:
            self.disconnect()
        
        return result
    
    def disconnect(self):
        """Close connection."""
        if self.socket:
            try:
                self.socket.close()
                logger.info("Connection closed")
            except:
                pass
            finally:
                self.socket = None
                self.connected = False
                self.authenticated = False

def main():
    """Main function to test CCcam connection."""
    # Server credentials
    config = {
        'host': 'storesat.live',
        'port': 45003,
        'username': 'storesat',
        'password': 'Gx$oNu5Wz#U0F',
        'timeout': 15
    }
    
    print("CCcam Protocol Test Client")
    print("=" * 40)
    print(f"Server: {config['host']}:{config['port']}")
    print(f"User: {config['username']}")
    print()
    
    # Create client and test connection
    client = CCcamProtocol(**config)
    result = client.test_connection()
    
    # Display results
    print("\n" + "=" * 40)
    print("TEST RESULTS")
    print("=" * 40)
    
    if result['success']:
        print("✓ Connection: SUCCESS")
        print(f"✓ Connection Time: {result['connection_time']:.2f}s")
        
        info = result['server_info']
        print(f"✓ Server Info:")
        print(f"  - Host: {info['host']}:{info['port']}")
        print(f"  - Connected: {info['connected']}")
        print(f"  - Authenticated: {info['authenticated']}")
        print(f"  - Version: {info['version']}")
        print(f"  - Cards: {info['cards']}")
        
    else:
        print("✗ Connection: FAILED")
        print(f"✗ Error: {result['error']}")
    
    print("\nNote: This is a simplified CCcam protocol implementation.")
    print("Full protocol support requires proper DES encryption and")
    print("complete message handling as per CCcam specifications.")

if __name__ == "__main__":
    main()